<template>
  <div class="flex gap-[30px]">
    <div class="w-[468px] h-full flex flex-col gap-[20px]">
      <InfoBox title="设备状态" height="525px" :infoType="1">
        <div class="flex flex-col p-[19px]">
          <div class="flex">
            <div
              v-for="(item, index) in stateCard"
              :key="index"
              class="flex items-center w-1/2 h-[60px]"
              :style="{
                'background-image': `url(${stateImgs[index]})`,
                'background-size': '100% 100%'
              }"
            >
              <div class="flex items-center justfity-center pl-[85px] text-map-simple-blue">
                <div class="text-base">{{ item.label }}</div>
                <p class="pl-[5px]">
                  <span
                    class="text-number font-bold"
                    :class="{
                      'text-[#13D1C1]': index === 0,
                      'text-[#FFF88B]': index === 1
                    }"
                    >{{ 122 }}</span
                  >
                  <span class="pl-[5px] text-base">个</span>
                </p>
              </div>
            </div>
          </div>
          <div class="flex-1">
            <div>
              <div class="text-[#fff] text-base pt-[15px] pb-[10px]">原生境保护点列表</div>
              <a-input size="large" placeholder="请输入关键字">
                <template #suffix>
                  <img :src="searchImg" alt="" style="width: 17px; height: 17px" />
                </template>
              </a-input>
            </div>
            <div class="mt-[10px] max-h-[320px] overflow-y-auto">
              <div
                v-for="(item, index) in bhd_data"
                :key="index"
                class="flex items-center w-full bg-[rgba(54,166,186,0.6)] py-[14px] px-[17px] mt-[5px] box-sizing cursor-pointer text-base text-[#EAFEFF]"
              >
                {{ item.bhdmc }}
              </div>
            </div>
          </div>
        </div>
      </InfoBox>

      <InfoBox title="历史数据" height="290px" :infoType="1">
        <div class="p-[20px]">
          <div>
            <a-range-picker class="w-full" size="large" @change="onRangeChange">
              <template #suffixIcon>
                <img :src="timeImg" alt="" style="width: 17px; height: 17px" />
              </template>
            </a-range-picker>
          </div>
          <div class="mt-[10px] max-h-[100px]">
            <a-table
              size="small"
              :scroll="{ y: 160 }"
              :pagination="false"
              :sticky="true"
              :bordered="false"
              :dataSource="dataSource"
              :columns="columns"
            >
              <template #bodyCell="{ column }">
                <template v-if="column.key === 'action'">
                  <span class="text-[#52DAF1]">查看</span>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </InfoBox>
    </div>
    <div class="flex-1">
      <InfoBox title="实时监控" :infoType="1">
        <div class="flex flex-wrap">
          <template v-for="(item, index) in mockData" :key="index">
            <div class="w-1/2 h-[295px] p-[23px] text-[#EAFEEF] border">
              <div class="text-base mb-[23px]">监控点{{ index + 1 }}环境数据</div>
              <div class="grid grid-cols-3 grid-rows-4 h-auto gap-2">
                <template v-for="([key, value], subIndex) in Object.entries(item)" :key="subIndex">
                  <div class="text-[#EAFEEF] border rounded-sm py-[8px] bg-[rgba(54,166,186,0.2)]">
                    <div class="text-base text-center font-medium">{{ key }} {{ value }}</div>
                  </div>
                </template>
              </div>
            </div>
          </template>
        </div>
      </InfoBox>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'Weather' })
import InfoBox from '@/app/views/OneMap/components/InfoBox/index.vue'
import { formDataKey } from '@/app/views/OneMap/config'

import searchImg from '@/assets/images/onemap/search.png'
import timeImg from '@/assets/images/onemap/time.png'
import card_7 from '@/assets/images/onemap/card_7.png'
import card_8 from '@/assets/images/onemap/card_8.png'

const formData = inject(formDataKey)

const stateCard = [
  {
    label: '在线',
    field: 'online',
    img: 'card_7'
  },
  {
    label: '离线',
    field: 'offline',
    img: 'card_8'
  }
]

const imageMap = {
  card_7,
  card_8
}

const stateImgs = stateCard.map((item) => imageMap[item.img])

const bhd_data = ref<any[]>([])

async function getFormData() {
  try {
    const list = formData?.value['protection_point_list']
    if (Array.isArray(list)) {
      bhd_data.value = list
    } else {
      bhd_data.value = []
    }
  } catch (error) {
    console.log('protection_point_list', error)
  }
}

onMounted(() => {
  getFormData()
})

const onRangeChange = (date, dateString) => {
  console.log(date, dateString)
}

const columns = [
  {
    title: '保护点',
    dataIndex: 'bhdmc',
    key: 'bhdmc',
    width: 160,
    ellipsis: true
  },
  {
    title: '日期',
    dataIndex: 'date',
    key: 'date',
    width: 100
  },
  {
    title: '温度',
    dataIndex: 'temperature',
    key: 'temperature'
  },
  {
    title: '湿度',
    dataIndex: 'humidity',
    key: 'humidity'
  },
  { title: '操作', key: 'action' }
]
const dataSource = [
  {
    key: '1',
    bhdmc: '湖北省钟祥市猕猴桃原生境保护点',
    date: '2023-01-01',
    temperature: '25°C',
    humidity: '60%'
  },
  {
    key: '2',
    bhdmc: '湖北省钟祥市猕猴桃原生境保护点',
    date: '2023-01-02',
    temperature: '26°C',
    humidity: '65%'
  },
  {
    key: '3',
    bhdmc: '湖北省钟祥市猕猴桃原生境保护点',
    date: '2023-01-02',
    temperature: '26°C',
    humidity: '65%'
  },
  {
    key: '4',
    bhdmc: '湖北省钟祥市猕猴桃原生境保护点',
    date: '2023-01-02',
    temperature: '26°C',
    humidity: '65%'
  },
  {
    key: '5',
    bhdmc: '湖北省钟祥市猕猴桃原生境保护点',
    date: '2023-01-02',
    temperature: '26°C',
    humidity: '65%'
  }
]

const mockData = [
  {
    温度: '25°C',
    湿度: '60%',
    风向: '东',
    'PM2.5': '10 µg/m³',
    'MP-10': '20 µg/m³',
    光照度: '200 lux',
    虫害: '无',
    植物病: '无',
    风速: '5 m/s',
    土壤温度: '20°C',
    土壤湿度: '50%',
    土壤PH值: '6.5'
  },
  {
    温度: '28°C',
    湿度: '65%',
    风向: '南',
    'PM2.5': '8 µg/m³',
    'MP-10': '18 µg/m³',
    光照度: '250 lux',
    虫害: '少量',
    植物病: '无',
    风速: '7 m/s',
    土壤温度: '22°C',
    土壤湿度: '55%',
    土壤PH值: '6.8'
  },
  {
    温度: '22°C',
    湿度: '58%',
    风向: '西',
    'PM2.5': '15 µg/m³',
    'MP-10': '25 µg/m³',
    光照度: '180 lux',
    虫害: '无',
    植物病: '轻微',
    风速: '4 m/s',
    土壤温度: '19°C',
    土壤湿度: '48%',
    土壤PH值: '6.3'
  },
  {
    温度: '26°C',
    湿度: '62%',
    风向: '北',
    'PM2.5': '12 µg/m³',
    'MP-10': '22 µg/m³',
    光照度: '300 lux',
    虫害: '无',
    植物病: '无',
    风速: '6 m/s',
    土壤温度: '21°C',
    土壤湿度: '52%',
    土壤PH值: '6.6'
  },
  {
    温度: '27°C',
    湿度: '63%',
    风向: '东',
    'PM2.5': '9 µg/m³',
    'MP-10': '17 µg/m³',
    光照度: '220 lux',
    虫害: '无',
    植物病: '轻微',
    风速: '5 m/s',
    土壤温度: '20°C',
    土壤湿度: '53%',
    土壤PH值: '6.7'
  },
  {
    温度: '24°C',
    湿度: '59%',
    风向: '南',
    'PM2.5': '13 µg/m³',
    'MP-10': '23 µg/m³',
    光照度: '280 lux',
    虫害: '无',
    植物病: '无',
    风速: '7 m/s',
    土壤温度: '22°C',
    土壤湿度: '55%',
    土壤PH值: '6.9'
  }
]
</script>

<style lang="less" scoped>
:deep(.ant-input-affix-wrapper) {
  background-color: #043e53;
  border-color: #3bb8c1;
  .ant-input {
    background-color: #043e53;
    color: #eafeff !important;
    &::placeholder {
      color: #eafeff;
    }
  }
}

:deep(.ant-picker) {
  background-color: #043e53;
  border-color: #3bb8c1;
  color: #eafeff;
  .ant-picker-input input::placeholder {
    color: #eafeff;
  }
  .ant-picker-input > input {
    color: #eafeff;
  }

  .ant-picker-range-separator {
    font-size: 0;

    svg {
      color: #eafeff;
    }
  }

  .ant-picker-clear {
    svg {
      width: 20px;
      height: 20px;
    }
  }
}

:deep(.ant-table-wrapper) {
  .ant-table-cell-row-hover {
    background: none !important;
  }
  .ant-table-thead > tr > th {
    background-color: #063e53;
    color: #eafeff;
  }
  .ant-table-body {
    background-color: #13414e;
    color: #eafeff;
  }
}
</style>
