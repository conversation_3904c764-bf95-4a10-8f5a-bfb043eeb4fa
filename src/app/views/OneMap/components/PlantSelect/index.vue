<template>
  <div>
    <div class="title">
      <img class="icon" :src="titleIcon" />
      <div class="text text-title">农业重点保护野生植物</div>
    </div>
    <a-select
      class="w-full"
      v-model:value="showValue"
      show-search
      allowClear
      placeholder="请选择植物"
      :default-active-first-option="false"
      :show-arrow="false"
      :filter-option="false"
      :not-found-content="null"
      :options="data"
      @search="handleSearch"
      @change="handleChange"
    ></a-select>
  </div>
</template>

<script lang="ts" setup>
import type { SelectProps } from 'ant-design-vue'
import titleIcon from '@/assets/images/onemap/title-icon.png'
import { formDataKey } from '../../config'

interface Props {
  value?: string | null
}

const props = defineProps<Props>()

const emit = defineEmits(['change'])
const formData = inject(formDataKey) as Ref<any>

const data = ref<SelectProps['options']>([
  {
    label: '国家一级保护植物',
    options: []
  },
  {
    label: '国家二级保护植物',
    options: []
  }
])
const showValue = ref()

const columns = ref<any[]>([])

const getFormData = async () => {
  try {
    const list = formData.value.wild_plant_list.filter((item) => item.sfnybmgl === '是')
    columns.value = list

    data.value = groupPlantsByLevel(list)
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}
const groupPlantsByLevel = (list) => {
  const firstLevel: any = []
  const secondLevel: any = []
  list.forEach((item) => {
    if (item.xbhdj === '一级') {
      firstLevel.push({ label: item.wzmc, value: item.wzmc })
    } else {
      secondLevel.push({ label: item.wzmc, value: item.wzmc })
    }
  })

  // 过滤掉无数据的分组
  return [
    { label: '国家一级保护植物', options: firstLevel },
    { label: '国家二级保护植物', options: secondLevel }
  ].filter((group) => group.options.length > 0) // 关键修改：只保留有数据的分组
}
onMounted(() => {
  getFormData()
})

const handleSearch = (val: string) => {
  if (!val) {
    data.value = groupPlantsByLevel(columns.value)
  } else {
    const filteredData = columns.value.filter((item) =>
      item.wzmc.toLowerCase().includes(val.toLowerCase())
    )
    data.value = groupPlantsByLevel(filteredData)
  }
}
const handleChange = (val: string) => {
  emit('change', val)
  data.value = groupPlantsByLevel(columns.value)
}

watch(
  () => props.value,
  (val) => {
    showValue.value = val
  }
)
</script>

<style lang="less" scoped>
.title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .icon {
    width: 22px;
    height: 32px;
    margin-right: 10px;
  }
  .text {
    font-weight: 500;
    color: transparent;
    background-image: linear-gradient(to bottom, #fff 20%, #00c1f6);
    -webkit-background-clip: text;
    background-clip: text;
    display: inline-block;
    position: relative;
  }
}
</style>
