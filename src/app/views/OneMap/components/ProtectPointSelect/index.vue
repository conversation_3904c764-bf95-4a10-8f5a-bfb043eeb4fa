<template>
  <div class="area-select">
    <div class="title">
      <img class="icon" :src="titleIcon" />
      <div class="text">原生境保护点列表</div>
    </div>
    <a-select
      class="w-full"
      v-model:value="value"
      :show-search="true"
      :allowClear="true"
      placeholder="请输入关键字"
      :default-active-first-option="false"
      :show-arrow="false"
      :filter-option="false"
      :not-found-content="null"
      :options="data"
      @search="handleSearch"
      @change="handleChange"
    ></a-select>
  </div>
</template>

<script lang="ts" setup>
import type { SelectProps } from 'ant-design-vue'
import titleIcon from '@/assets/images/onemap/title-icon.png'

interface Props {
  list: any[]
  labelKey?: string
  valueKey?: string
}
const props = withDefaults(defineProps<Props>(), {
  labelKey: 'bhdmc',
  valueKey: 'bhdbm'
})
const emit = defineEmits(['change'])

const data = ref<SelectProps['options']>([])
const value = ref()

const formData = computed(() => {
  return props.list
})

onMounted(() => {
  data.value = groupPlantsByLevel(formData.value)
})

const groupPlantsByLevel = (list) => {
  return list.map((item) => ({
    label: item[props.labelKey],
    value: item[props.valueKey]
  }))
}

const handleSearch = (val: string) => {
  if (!val) {
    data.value = groupPlantsByLevel(formData.value)
  } else {
    const filteredData = formData.value.filter((item) =>
      item[props.labelKey].toLowerCase().includes(val.toLowerCase())
    )
    data.value = groupPlantsByLevel(filteredData)
  }
}
const handleChange = (val: string) => {
  emit('change', val)
  if (!val) {
    value.value = null
    data.value = groupPlantsByLevel(formData.value)
  } else {
    value.value = val
  }
}
</script>

<style lang="less" scoped>
.area-select {
  box-sizing: border-box;
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .icon {
      width: 22px;
      height: 32px;
      margin-right: 10px;
    }
    .text {
      font-size: 22px;
      font-weight: 500;
      color: transparent;
      background-image: linear-gradient(to bottom, #fff 20%, #00c1f6);
      -webkit-background-clip: text;
      background-clip: text;
      display: inline-block;
      position: relative;
    }
  }
}
.ant-select {
  width: 100%;
}
</style>
