<template>
  <div class="area-select">
    <div class="title">
      <img class="icon" :src="titleIcon" />
      <div class="text text-title">行政区划</div>
    </div>
    <a-select
      v-model:value="value"
      placeholder="请选择行政区划"
      allowClear
      :options="options"
      @change="onChange"
    />
  </div>
</template>

<script lang="ts" setup>
import type { CascaderProps } from 'ant-design-vue'
import { useOneMapStore } from '@/stores'
import titleIcon from '@/assets/images/onemap/title-icon.png'

const oneMapStore = useOneMapStore()

const options = ref<CascaderProps['options']>([])
const value = ref<string[]>([])

const cityList = ref<any[]>([])

// 请求行政区划
const getArea = async () => {
  try {
    cityList.value = oneMapStore.cityDicts

    // 处理数据生成级联选项
    options.value = cityList.value.map((city) => ({
      value: city.ccode,
      label: city.cname
    }))
  } catch (error) {
    console.error('获取行政区划数据失败:', error)
  }
}

const onChange = (val: string) => {
  if (!val) {
    oneMapStore.clearSelectedArea()
    return
  }
  const selectedAreaInfo = cityList.value.find((city) => city.ccode === val)
  oneMapStore.setSelectedArea(selectedAreaInfo)
}

onMounted(async () => {
  await getArea()
  value.value = oneMapStore.selectedArea ? oneMapStore.selectedArea.ccode : null
})

watch(
  () => oneMapStore.selectedArea,
  (val) => {
    value.value = val ? val.ccode : null
  }
)
</script>

<style lang="less" scoped>
.area-select {
  box-sizing: border-box;
  margin-bottom: 30px;
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .icon {
      width: 22px;
      height: 32px;
      margin-right: 10px;
    }
    .text {
      font-weight: 500;
      color: transparent;
      background-image: linear-gradient(to bottom, #fff 20%, #00c1f6);
      -webkit-background-clip: text;
      background-clip: text;
      display: inline-block;
      position: relative;
    }
  }
}
.ant-select {
  width: 100%;
}
</style>
